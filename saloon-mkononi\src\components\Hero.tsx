const Hero = () => {
  return (
    <section className="flex flex-col md:flex-row mt-12 lg:mt-20">
      <div className="w-full md:w-1/2 flex items-center justify-center p-12 lg:p-24 xl:p-32">
        <div className="max-w-md text-left">
          <h1 className="font-serif-display text-5xl lg:text-5xl font-normal leading-snug">
            <span className="block">Beauty Services</span>
            <span className="italic">At Your Fingertips</span>
          </h1>
          <p className="mt-8 text-[11px] leading-relaxed font-light text-gray-400">
            Discover and book appointments with the best salons and stylists in your city. Saloon Mkononi makes finding your next look effortless.
          </p>
          <p className="mt-5 text-[11px] leading-relaxed font-light text-gray-400">
            Browse services, read real reviews, and book your spot 24/7. Say goodbye to waiting and hello to convenience, all from the palm of your hand.
          </p>
          <div className="mt-12">
            <a href="#" className="border border-white/70 px-6 py-2.5 rounded-full hover:bg-white hover:text-black transition-colors duration-300 text-[10px] uppercase tracking-wider">Explore Salons</a>
          </div>
        </div>
      </div>
      <div className="w-full md:w-1/2 h-[60vh] md:h-auto">
        <img src="../public/photos/phoneOne.jpeg" alt="A woman with short dark hair looking over her shoulder" className="w-full h-full object-cover" />
      </div>
    </section>
  )
}

export default Hero
